import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class RlsPolicyEditorScreen extends ConsumerStatefulWidget {
  const RlsPolicyEditorScreen({super.key});

  @override
  ConsumerState<RlsPolicyEditorScreen> createState() =>
      _RlsPolicyEditorScreenState();
}

class _RlsPolicyEditorScreenState extends ConsumerState<RlsPolicyEditorScreen> {
  final TextEditingController _sqlController = TextEditingController();
  bool _isExecuting = false;
  String _result = '';
  String _error = '';

  @override
  void dispose() {
    _sqlController.dispose();
    super.dispose();
  }

  Future<void> _executeQuery() async {
    if (_sqlController.text.trim().isEmpty) {
      setState(() {
        _error = 'Please enter a SQL query';
        _result = '';
      });
      return;
    }

    setState(() {
      _isExecuting = true;
      _error = '';
      _result = '';
    });

    try {
      final response = await Supabase.instance.client
          .rpc('execute_sql', params: {'query': _sqlController.text.trim()});

      setState(() {
        _result =
            'Query executed successfully!\nResult: ${response.toString()}';
        _error = '';
      });
    } catch (e) {
      setState(() {
        _error = 'Error executing query: ${e.toString()}';
        _result = '';
      });
    } finally {
      setState(() {
        _isExecuting = false;
      });
    }
  }

  void _loadSamplePolicy() {
    _sqlController.text = '''-- Sample RLS Policy for orders table
CREATE POLICY "Users can view their own orders" ON orders
FOR SELECT USING (
  auth.uid() = customer_id OR
  auth.uid() = (SELECT owner_id FROM restaurants WHERE id = restaurant_id) OR
  auth.uid() = rider_id OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'super_admin'
  )
);''';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('RLS Policy Editor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('RLS Policy Help'),
                  content: const SingleChildScrollView(
                    child: Text('''
Row Level Security (RLS) Policy Editor

This tool allows super admins to create and modify RLS policies for database tables.

Common RLS patterns:
• auth.uid() - Current user's ID
• role() - Current user's role
• SELECT/INSERT/UPDATE/DELETE - Policy types

Example:
CREATE POLICY "policy_name" ON table_name
FOR SELECT USING (condition);

⚠️ Warning: Be careful with RLS policies as they affect data security!
                    '''),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Close'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // SQL Input Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          'SQL Query',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        TextButton.icon(
                          onPressed: _loadSamplePolicy,
                          icon: const Icon(Icons.code),
                          label: const Text('Load Sample'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      height: 200,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextField(
                        controller: _sqlController,
                        maxLines: null,
                        expands: true,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 14,
                        ),
                        decoration: const InputDecoration(
                          hintText: 'Enter your SQL query here...',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.all(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isExecuting ? null : _executeQuery,
                        icon: _isExecuting
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.play_arrow),
                        label: Text(
                            _isExecuting ? 'Executing...' : 'Execute Query'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Results Section
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Results',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SingleChildScrollView(
                            child: Text(
                              _error.isNotEmpty
                                  ? _error
                                  : _result.isNotEmpty
                                      ? _result
                                      : 'No results yet. Execute a query to see results here.',
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 14,
                                color: _error.isNotEmpty
                                    ? Colors.red
                                    : Colors.black87,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Warning Section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                border: Border.all(color: Colors.orange.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Warning: RLS policies affect database security. Test thoroughly before applying to production.',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
